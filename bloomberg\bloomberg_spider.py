import requests
import mysql.connector
from mysql.connector import Error
import datetime
from urllib.parse import urljoin
import logging
# 配置参数
FEED_URL = "https://www.bloomberg.com/lineup-next/api/stories?limit=25&pageNumber=1&types=ARTICLE,FEATURE,INTERACTIVE,LETTER,EXPLAINERS"
DB_CONFIG = {
    "host": "127.0.0.1",
    "user": "root",  # 替换为MySQL用户名
    "password": "root",  # 替换为MySQL密码
    "database": "owl_admin",  # 提前创建好数据库
    "port": 3307
}
TIMESTAMP_FILE = "last_update.txt"
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


def get_last_update():
    try:
        with open(TIMESTAMP_FILE, "r") as f:
            timestamp_str = f.read().strip()
            return datetime.datetime.fromisoformat(timestamp_str).replace(tzinfo=datetime.timezone.utc)
    except FileNotFoundError:
        return datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=48)

def save_last_update(timestamp):
    with open(TIMESTAMP_FILE, "w") as f:
        f.write(timestamp.isoformat())


def fetch_feed():
    """获取JSON订阅源数据"""
    try:
        response = requests.get(FEED_URL, headers=HEADERS, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logging.error(f"请求订阅源失败: {e}")
        return None


def bjs_date(utc_time):

    # 将字符串转换为 datetime 对象（UTC 时间）
    utc_datetime = datetime.datetime.strptime(utc_time, "%Y-%m-%dT%H:%M:%S.%fZ")

    # 转换为北京时间（UTC+8）
    beijing_datetime = utc_datetime + datetime.timedelta(hours=8)

    # 格式化输出
    beijing_time = beijing_datetime.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    return beijing_time


def parse_items(feed_data):
    """解析JSON中的新闻条目"""
    items = []
    for item in feed_data.get("items", []):
        # 提取关键字段（根据实际JSON结构调整）
        item_id = item.get("id")  # 优先使用唯一ID
        url = item.get("url") or urljoin(FEED_URL, item.get("external_url"))
        summary = item.get("summary")
        content_html = item.get("content_html")
        title = item.get("title")
        content = item.get("content_text") or item.get("summary")
        image_url = item.get("image")
        # 解析时间戳
        time_str = item.get("date_published") or item.get("date_modified")
        bj_date = bjs_date(time_str)
        if not time_str:
            continue
        try:
            time_str = time_str.replace('Z', '+00:00')
            published_time = datetime.datetime.fromisoformat(time_str)
        except ValueError:
            time_str = time_str[:-1]
            published_time = datetime.datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%S%z")
        # 统一为UTC时区
        if published_time.tzinfo is None:
            published_time = published_time.replace(tzinfo=datetime.timezone.utc)
        else:
            published_time = published_time.astimezone(datetime.timezone.utc)
        items.append({
            "id_hash": str(item_id) if item_id else hash(url),  # 强制转为字符串
            "title": title,
            "url": url,
            "content": content,
            "summary": summary,
            "content_html": content_html,
            "image_url": image_url,
            "published_at": published_time,
            "bj_date": bj_date
        })
    return items


def save_items(items):
    """去重保存到MySQL"""
    inserted_count = 0
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        for item in items:
            # 使用 INSERT IGNORE 避免重复
            cursor.execute('''
                INSERT IGNORE INTO `admin_bloomberg_news` 
                (id_hash, title,summary, url, content, published_at,content_html, image_url, bj_date)
                VALUES (%s, %s, %s, %s, %s,%s, %s, %s, %s)
            ''', (
                item["id_hash"],
                item["title"],
                item["summary"],
                item["url"],
                item["content"],
                item["published_at"].strftime("%Y-%m-%d %H:%M:%S"),
                item["content_html"],
                item["image_url"],
                item["bj_date"],
            ))

            if cursor.rowcount > 0:
                inserted_count += 1
        conn.commit()
        logging.info(f"实际插入 {inserted_count} 条数据")
    except Error as e:
        logging.error(f"数据库写入失败: {e}")
        conn.rollback()
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()


def main():
    last_update = get_last_update()
    logging.info(f"上次更新时间: {last_update}")

    feed_data = fetch_feed()
    print(feed_data)
    exit(1)
    if not feed_data:
        return

    items = parse_items(feed_data)

    if not items:
        logging.info("未解析到有效条目")
        return


    # 过滤新增条目（根据发布时间）
    new_items = [item for item in items if item["published_at"] > last_update]

    # new_items_sorted = sorted(items, key=lambda x: x["published_at"])
    # # latest_time = new_items_sorted[-1]["published_at"]
    #
    # save_items(new_items_sorted)
    if new_items:
        # 按时间排序并获取最新时间
        new_items_sorted = sorted(new_items, key=lambda x: x["published_at"])
        latest_time = new_items_sorted[-1]["published_at"]

        save_items(new_items_sorted)
        save_last_update(latest_time)
        logging.info(f"最新时间戳已更新至: {latest_time}")
    else:
        logging.info("无新增数据")


if __name__ == "__main__":
    main()

import requests
import json
import datetime
import pandas as pd
import matplotlib.pyplot as plt
import configparser
import redis
import mysql.connector


# 读取配置文件
# config = configparser.ConfigParser()
# config.read('config/config.ini')
#
# # 配置 MySQL 数据库连接
# db_config = {
#     "host": config.get('MySQL', 'host'),
#     "user": config.get('MySQL', 'user'),
#     "password": config.get('MySQL', 'password'),
#     "database": config.get('MySQL', 'database')
# }
#
# # 配置 Redis 连接
# redis_config = {
#     "host": config.get('Redis', 'host'),
#     "port": config.getint('Redis', 'port'),
#     "db": config.getint('Redis', 'db')
# }
#
# redis_client = redis.Redis(**redis_config)

class DeepSeekFinancialAnalyzer:
    def __init__(self, api_key, model_name="deepseek-reasoner"):
        self.api_url = "https://api.deepseek.com/v1/chat/completions"
        self.api_key = 'sk-a6285fd909dc48c981145cf83ff3a465'
        self.model_name = model_name
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 金融领域专业词库（可扩展）
        self.finance_terms = {
            "量化宽松": "positive", "加息": "negative", "降息": "positive",
            "财报超预期": "strong_positive", "盈利预警": "strong_negative",
            "并购": "positive", "分拆": "neutral", "IPO": "positive"
        }

    def _generate_analysis_prompt(self, news_text):
        """构建专业金融分析提示词"""
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")

        return f"""
# Role: 财经热点分析师

## Profile
- language: 中文
- description: 专业分析筛选财经新闻热点，识别市场趋势和投资机会
- background: 具备金融学背景和新闻分析经验
- personality: 严谨、敏锐、客观
- expertise: 财经新闻分析、市场趋势研判、投资机会识别
- target_audience: 投资者、金融从业者、企业决策者

## Skills

1. 新闻筛选能力
   - 热点识别: 快速识别财经领域重要新闻
   - 相关性评估: 判断新闻对市场的影响程度
   - 时效性判断: 评估新闻的时效价值和持续影响
   - 可信度验证: 核实新闻来源和内容真实性

2. 分析解读能力
   - 趋势分析: 从新闻中提炼市场趋势
   - 影响评估: 分析新闻对行业和个股的影响
   - 关联分析: 发现不同新闻事件间的关联性
   - 深度解读: 提供专业视角的新闻解读

## Rules

1. 基本原则：
   - 客观中立: 保持分析的中立性和客观性
   - 数据驱动: 基于事实和数据进行分析
   - 全面覆盖: 涵盖主要财经领域和市场
   - 价值导向: 聚焦有实际投资价值的新闻

2. 行为准则：
   - 及时响应: 对突发财经新闻快速反应
   - 持续跟踪: 对重要事件保持持续关注
   - 风险提示: 明确提示潜在投资风险
   - 专业表述: 使用规范专业的财经术语

3. 限制条件：
   - 不提供投资建议: 仅分析不推荐具体操作
   - 不传播未证实消息: 只分析已核实新闻
   - 不涉及内幕信息: 严格遵守信息披露规定
   - 不夸大新闻影响: 保持理性客观评估

## Workflows

- 目标: 筛选出最具价值的财经热点新闻
- 步骤 1: 收集主流财经媒体和官方渠道新闻
- 步骤 2: 初步筛选具有市场影响力的新闻
- 步骤 3: 评估新闻的真实性、时效性和影响力
- 步骤 4: 分析新闻对市场和行业的潜在影响
- 预期结果: 提供3-5条最具价值的财经热点分析

## OutputFormat

1. 输出格式类型：
   - format: markdown
   - structure: 标题+摘要+分析+影响评估
   - style: 专业简洁
   - special_requirements: 包含数据支持和来源说明

2. 格式规范：
   - indentation: 二级标题缩进2空格
   - sections: 明确分节，每节有标题
   - highlighting: 关键数据加粗显示

3. 验证规则：
   - validation: 每条新闻必须有可靠来源
   - constraints: 每条分析不超过300字
   - error_handling: 发现错误立即更正并标注

4. 示例说明：
   1. 示例1：
      - 标题: 央行降准0.5个百分点
      - 格式类型: markdown
      - 说明: 货币政策热点分析
      - 示例内容: |
          ## 央行宣布下调存款准备金率0.5个百分点
          
          **摘要**: 中国人民银行决定自2023年9月15日起下调金融机构存款准备金率0.5个百分点(不含已执行5%存款准备金率的金融机构)。
          
          **分析**: 此次降准释放长期资金约1万亿元，旨在支持实体经济发展，降低社会融资成本。
          
          **影响评估**: 
          - 利好银行板块: 缓解银行负债压力
          - 提振市场信心: 释放宽松政策信号
          - 需关注后续LPR调整情况

## Initialization
作为财经热点分析师，你必须遵守上述Rules，按照Workflows执行任务，并按照输出格式输出。

## 待分析新闻
发布时间：{current_date}
新闻内容：
{news_text}
"""

    def analyze_news(self, news_text):
        """执行财经新闻分析"""
        try:
            prompt = self._generate_analysis_prompt(news_text)

            payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.2,  # 降低随机性
                "max_tokens": 1500,
                # "response_format": {"type": "json_object"}
            }

            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=json.dumps(payload)
            )

            print(response.text)

            if response.status_code != 200:
                raise Exception(f"API Error: {response.status_code} - {response.text}")

            result = response.json()
            analysis_result = json.loads(result['choices'][0]['message']['content'])

            return {
                "status": "success",
                "analysis": analysis_result
            }

        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def visualize_results(self, analysis_result, save_path=None):
        """可视化分析结果"""
        if not analysis_result or "sentiment_analysis" not in analysis_result:
            print("无效的分析结果")
            return

        # 情感分析可视化
        sentiment = analysis_result["sentiment_analysis"]
        plt.figure(figsize=(15, 10))

        # 情感评分仪表盘
        plt.subplot(2, 2, 1)
        plt.title("情感分析仪表盘", fontsize=14)
        plt.barh(0, sentiment["score"], height=0.5,
                 color='green' if sentiment["score"] > 0 else 'red')
        plt.xlim(-1, 1)
        plt.axvline(0, color='black', linestyle='--')
        plt.yticks([])
        plt.xlabel("情感评分 (-1 至 +1)")

        # 关键词云
        plt.subplot(2, 2, 2)
        kw_counts = {kw: 5 + abs(sentiment["score"]) * 20 for kw in sentiment["keywords"]}
        plt.title("情感关键词云", fontsize=14)
        plt.scatter(
            x=range(len(sentiment["keywords"])),
            y=[1] * len(sentiment["keywords"]),
            s=list(kw_counts.values()),
            alpha=0.6
        )
        for i, kw in enumerate(sentiment["keywords"]):
            plt.text(i, 1, kw, ha='center', va='center', fontsize=12)
        plt.ylim(0.5, 1.5)
        plt.axis('off')

        # 市场预测热力图
        plt.subplot(2, 1, 2)
        predictions = analysis_result["market_prediction"]
        sectors = {
            "股票": predictions["equity_impact"],
            "大宗商品": predictions["commodity_impact"],
            "行业ETF": predictions["sector_etf_impact"]
        }

        impact_matrix = []
        for sector, data in sectors.items():
            # 将方向转换为数值
            direction_map = {"down": -1, "sideways": 0, "up": 1}
            magnitude_map = {"low": 0.3, "medium": 0.6, "high": 1.0}
            impact = direction_map[data["direction"]] * magnitude_map[data["magnitude"]]
            impact_matrix.append([sector, impact, data["confidence"]])

        df = pd.DataFrame(impact_matrix, columns=["Sector", "Impact", "Confidence"])
        plt.imshow([df["Impact"]], cmap="RdYlGn", aspect="auto", vmin=-1, vmax=1)
        plt.colorbar(label="市场影响程度")
        plt.xticks(range(len(df)), df["Sector"])
        plt.title("市场影响预测热力图", fontsize=14)

        # 添加置信度标注
        for i, conf in enumerate(df["Confidence"]):
            plt.text(i, 0, f"{conf * 100:.0f}%",
                     ha='center', va='bottom', fontsize=10, color='black')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300)
            print(f"图表已保存至: {save_path}")
        else:
            plt.show()


# 使用示例 =====================================================================
if __name__ == "__main__":
    # 配置你的DeepSeek API密钥
    API_KEY = "***********************************"

    # 连接数据库
    # db = mysql.connector.connect(**db_config)
    # cursor = db.cursor()
    #
    # # 获取当前日期
    # now = datetime.datetime.now()
    # current_date = now.strftime('%Y-%m-%d')
    #
    # # 定义查询语句和参数，使用参数化查询，避免 SQL 注入
    # query = "SELECT id,content,created_at,is_hot FROM lcdp_all_news WHERE is_hot = %s AND DATE(created_at) = %s"
    # cursor.execute(query, (1,current_date))
    #
    # # 获取查询结果
    # results = cursor.fetchall()
    # print(results)
    # exit(5)
    # 提取某一列字段，例如 'title'
    # ids = [row[0] for row in results]
    #
    # # 将列表转换为用逗号隔开的字符串
    # ids_str = ",".join(str(id) for id in ids)
    # titles = [row[1] for row in results]
    #
    # financial_news = titles
    # print(financial_news)
    # 示例财经新闻
    financial_news = """
    格隆汇7月4日｜据Counterpoint Research的数据，
    今年第二季度，iPhone在中国销量同比增长8%，
    是自2023年第二季以来苹果首次在中国市场录得增长。
    苹果销售有所提升，主要是因为中国电商平台对其最新iPhone 16提供折扣促销，同时提高部分iPhone的以旧换新的价格，
    消费者反应良好，尤其是在618购物节的前一星期。Counterpoint称，华为第二季销量同比增长12%，
    该公司是第二季中国市占率最大的手机厂商，其次是Vivo，苹果则排行第三。
    """

    # 初始化分析器
    analyzer = DeepSeekFinancialAnalyzer(api_key=API_KEY)

    # 执行分析
    result = analyzer.analyze_news(financial_news)

    print(result)
    exit(1)

    if result["status"] == "success":
        print("分析成功！")
        print("=" * 50)

        # 打印结构化结果
        analysis = result["analysis"]
        print(analysis['market_prediction'])
        exit(3)
        # 情感分析结果
        print("\n[情感分析]")
        print(f"综合评分: {analysis['sentiment_analysis']['score']:.2f}")
        print(f"趋势: {analysis['sentiment_analysis']['trend']}")
        print(f"关键词: {', '.join(analysis['sentiment_analysis']['keywords'])}")
        print(f"置信度: {analysis['sentiment_analysis']['confidence'] * 100:.0f}%")

        # 内容解读
        print("\n[内容解读]")
        print("核心事件:")
        for event in analysis['content_interpretation']['core_events']:
            print(f" - {event}")

        print("\n影响链推理:")
        for impact in analysis['content_interpretation']['impact_chain']:
            print(f" → {impact}")

        # 市场预测
        print("\n[市场预测]")
        equity = analysis['market_prediction']['equity_impact']
        print(f"股票预测: {equity['direction']} ({equity['magnitude']} impact)")
        print(f"置信度: {equity['confidence'] * 100:.0f}% | 时间: {analysis['market_prediction']['time_horizon']}")

        # 可视化结果,暂时不用
        # analyzer.visualize_results(analysis, save_path="financial_analysis.png")

        # 插入数据
        sql = """
                                    INSERT INTO lcdp_analysis_deepseek (news_id, sentiment_analysis, score, trend, keywords, sentiment_analysis_confidence,content_interpretation,core_events,impact_chain,market_prediction,direction,confidence,created_at)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                    """

        val = (
            ids_str,
            "情感分析",
            f"综合评分: {analysis['sentiment_analysis']['score']:.2f}",
            f"趋势: {analysis['sentiment_analysis']['trend']}",
            f"关键词: {', '.join(analysis['sentiment_analysis']['keywords'])}",
            f"置信度: {analysis['sentiment_analysis']['confidence'] * 100:.0f}%",
            "内容解读",
            f"核心事件: {','.join(analysis['content_interpretation']['core_events'])}",
            f"影响链推理: {','.join(analysis['content_interpretation']['impact_chain'])}",
            "市场预测",
            f"股票预测: {equity['direction']} ({equity['magnitude']} impact)",
            f"置信度: {equity['confidence'] * 100:.0f}% | 时间: {analysis['market_prediction']['time_horizon']}",
            now
        )
        cursor.execute(sql, val)
        db.commit()
        # 关闭连接
        cursor.close()
        db.close()
        print(f"新增分析数据到数据库中")

    else:
        print(f"分析失败: {result['message']}")
# ```
#
# ## 关键功能说明
#
# ### 1. 三重分析架构
# - ** 情感分析 **：量化评分 + 关键词提取
# - ** 内容解读 **：事件提取 + 影响链推理
# - ** 市场预测 **：多资产类别影响预测
#
# ### 2. 专业提示词工程
# ```python
#
#
# def _generate_analysis_prompt(self, news_text):
#     """构建专业金融分析提示词"""
#     # 包含角色设定、分析维度要求、输出格式规范
#     # 注入金融领域专业术语词典
#     # 要求结构化JSON输出
#
#
# ```
#
# ### 3. 可视化模块
# ```python
#
#
# def visualize_results(self, analysis_result, save_path=None):
#     """可视化分析结果"""
#     # 情感评分仪表盘
#     # 关键词云图
#     # 市场影响热力图
#
#
# ```
#
# ## 输出示例
# ```json
# {
#     "sentiment_analysis": {
#         "score": -0.85,
#         "trend": "strong_negative",
#         "keywords": ["鹰派信号", "加息", "下调增长预期"],
#         "confidence": 0.92
#     },
#     "content_interpretation": {
#         "core_events": [
#             "美联储维持利率但释放鹰派信号",
#             "点阵图显示可能再加息两次",
#             "下调2023年GDP增长预期至1.2%"
#         ],
#         "impact_chain": [
#             "货币政策紧缩 → 融资成本上升",
#             "增长预期下调 → 企业盈利承压",
#             "风险偏好下降 → 科技股抛售压力增大"
#         ],
#         "historical_comparison": "类似2022年6月加息周期市场反应"
#     },
#     "market_prediction": {
#         "equity_impact": {
#             "direction": "down",
#             "magnitude": "high",
#             "confidence": 0.88
#         },
#         "commodity_impact": {
#             "direction": "down",
#             "magnitude": "medium",
#             "confidence": 0.75
#         },
#         "sector_etf_impact": {
#             "direction": "down",
#             "magnitude": "high",
#             "confidence": 0.85
#         },
#         "time_horizon": "72h"
#     }
# }
# ```
#
# ## 可视化效果
# ![Financial Analysis Visualization](financial_analysis.png)
# - 左侧：情感评分仪表盘和关键词云
# - 右侧：多资产市场影响热力图（颜色越红表示负面影响越大）
#
# ## 优化建议
#
# 1. ** 领域知识增强 **
# ```python
# # 在初始化函数中添加行业特定词典
# self.sector_terms = {
#     "科技": ["芯片", "AI", "算力"],
#     "能源": ["原油", "OPEC", "钻井平台"]
# }
#
# # 在提示词中注入行业特定术语
# ```
#
# 2. ** 历史数据对比 **
# ```python
#
#
# # 可扩展加入历史数据比对模块
# def _add_historical_context(self, company_ticker):
#
#
# # 调用金融数据库API获取历史事件
# # 返回相似历史事件列表
# ```
#
# 3. ** 实时市场数据整合 **
# ```python
# # 分析结果后自动获取实时行情
# import yfinance as yf
#
#
# def get_real_time_quotes(self, tickers):
#     data = yf.download(tickers, period="1d", interval="5m")
#     return data.tail(3)  # 返回最近3个时间点数据
#
#
# ```
#
# 4. ** 置信度校准 **
# ```python
#
#
# # 添加置信度校准机制
# def calibrate_confidence(self, analysis_result):
#
#
# # 根据关键词出现频率调整置信度
# # 根据历史预测准确率动态调整
# ```
#
# ## 系统部署建议
#
# 1. ** 性能优化 **
# - 使用异步请求处理批量新闻
# - 实现结果缓存机制（相同新闻MD5哈希校验）
#
# 2. ** 安全增强 **
# - API密钥轮换管理
# - 请求速率限制（避免触发API限制）
#
# 3. ** 企业级集成 **
# ```mermaid
# graph
# LR
# A[新闻源] --> B[DeepSeek分析引擎]
# B --> C[结果存储]
# C --> D[交易系统]
# C --> E[风险控制]
# C --> F[研报生成]
# ```
#
# 此方案提供了从API调用到结果可视化的完整工作流，特别适用于：
# - 量化交易信号生成
# - 实时风险监控
# - 投研报告自动化
# - 财经信息聚合平台
#
# 需要根据实际API文档调整请求参数，并建议添加错误处理和重试机制以提高系统稳定性。
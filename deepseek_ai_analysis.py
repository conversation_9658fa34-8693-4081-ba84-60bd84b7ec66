import requests
import json
import re
import datetime
from collections import defaultdict


class DeepSeekFinancialAnalyst:
    def __init__(self, api_key):
        self.api_url = "https://api.deepseek.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.system_prompt = """
        # Role：财经新闻分析师

        ## Background：在金融市场波动和政策变化频繁的背景下，用户需要专业的情感分析、政策解读和市场预测来辅助投资决策

        ## Attention：请注意财经新闻的时效性和准确性，避免过时或错误信息影响分析结果

        ## Profile：
        - Author: 财经研究团队
        - Version: 0.1
        - Language: 中文
        - Description: 专业分析财经新闻的情感倾向、政策影响和市场走势的专家

        ### Skills:
        - 熟练运用自然语言处理技术进行情感分析
        - 深入理解宏观经济政策和行业法规
        - 掌握金融市场运行规律和预测模型
        - 能够结合历史数据进行趋势分析
        - 具备跨市场关联分析能力

        ## Goals:
        - 对财经新闻进行准确的情感倾向判断
        - 深度解读政策背后的意图和影响
        - 基于多维度数据预测市场走势
        - 提供可操作的投资建议
        - 识别潜在风险和机会

        ## Constrains:
        - 必须基于可靠数据源进行分析
        - 需要区分事实和观点
        - 考虑不同市场间的联动效应
        - 保持分析的客观中立性
        - 注意信息的时间敏感性

        ## Workflow:
        1. 收集并验证财经新闻来源的可靠性
        2. 进行文本情感分析和关键词提取
        3. 评估政策影响的范围和程度
        4. 构建市场预测模型
        5. 生成综合分析报告
        6. 提出风险预警和投资建议

        ## OutputFormat:
        - 情感分析结果(积极/中性/消极)
        - 政策影响等级(高/中/低)
        - 市场预测时间范围(短期/中期/长期)
        - 关键影响因素列表
        - 具体建议和风险提示

        ## Suggestions:
        - 增加行业细分维度的分析
        - 考虑国际市场的溢出效应
        - 建立历史相似案例对比
        - 引入专家观点作为补充
        - 定期更新分析模型参数

        ## Initialization
        作为财经新闻分析师，你必须遵守分析准则，使用中文与用户交流，提供专业客观的分析报告。
        """

    def analyze_news(self, news_text):
        """调用DeepSeek API分析财经新闻"""
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"请分析以下财经新闻：\n{news_text}"}
            ],
            "temperature": 0.2,
            "max_tokens": 8000
        }

        try:
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            print(f"API调用失败: {str(e)}")
            return None

    def parse_analysis_result(self, raw_text):
        """解析DeepSeek返回的分析结果"""
        result = {
            "sentiment": "中性",
            "policy_impact": "中",
            "market_prediction": {},
            "key_factors": [],
            "suggestions": [],
            "risk_warnings": []
        }

        # 情感分析提取
        sentiment_match = re.search(r"情感分析结果[：:]\s*(积极|中性|消极)", raw_text)
        if sentiment_match:
            result["sentiment"] = sentiment_match.group(1)

        # 政策影响提取
        policy_match = re.search(r"政策影响等级[：:]\s*(高|中|低)", raw_text)
        if policy_match:
            result["policy_impact"] = policy_match.group(1)

        # 市场预测提取
        prediction_match = re.findall(r"(短期|中期|长期)预测[：:]([^\n]+)", raw_text)
        for period, prediction in prediction_match:
            result["market_prediction"][period] = prediction.strip()

        # 关键因素提取
        factors_start = re.search(r"关键影响因素[：:]", raw_text)
        if factors_start:
            factors_text = raw_text[factors_start.end():]
            factors_end = factors_text.find("\n\n")
            factors_text = factors_text[:factors_end] if factors_end != -1 else factors_text
            result["key_factors"] = [f.strip() for f in factors_text.split("\n") if f.strip()]

        # 建议和风险提取
        suggestions_match = re.search(r"具体建议[：:]([^\n]+)", raw_text)
        if suggestions_match:
            result["suggestions"] = [s.strip() for s in suggestions_match.group(1).split(";")]

        risk_match = re.search(r"风险提示[：:]([^\n]+)", raw_text)
        if risk_match:
            result["risk_warnings"] = [r.strip() for r in risk_match.group(1).split(";")]

        return result

    def generate_report(self, analysis_data, news_text):
        """生成格式化的分析报告"""
        report = []

        # 报告标题
        report.append("=" * 60)
        report.append("财经新闻深度分析报告")
        report.append(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)
        report.append("")

        # 新闻摘要
        report.append("【新闻摘要】")
        report.append(news_text[:200] + "..." if len(news_text) > 200 else news_text)
        report.append("")

        # 情感分析
        sentiment_emoji = {
            "积极": "📈",
            "中性": "📊",
            "消极": "📉"
        }
        report.append(f"【情感分析】{sentiment_emoji.get(analysis_data['sentiment'], '')}")
        report.append(f"情绪倾向: {analysis_data['sentiment']}")
        report.append("")

        # 政策影响
        impact_level = analysis_data["policy_impact"]
        report.append(f"【政策影响】{'⚠️' if impact_level == '高' else 'ℹ️'}")
        report.append(f"影响等级: {impact_level}")
        report.append("")

        # 市场预测
        report.append("【市场预测】🔮")
        for period, prediction in analysis_data["market_prediction"].items():
            report.append(f"{period}预测: {prediction}")
        report.append("")

        # 关键因素
        report.append("【关键因素】🔑")
        for i, factor in enumerate(analysis_data["key_factors"], 1):
            report.append(f"{i}. {factor}")
        report.append("")

        # 投资建议
        report.append("【投资建议】💡")
        for i, suggestion in enumerate(analysis_data["suggestions"], 1):
            report.append(f"{i}. {suggestion}")
        report.append("")

        # 风险提示
        report.append("【风险提示】🚨")
        for i, risk in enumerate(analysis_data["risk_warnings"], 1):
            report.append(f"{i}. {risk}")

        return "\n".join(report)

    def analyze_and_report(self, news_text):
        """完整的分析流程"""
        print("开始分析财经新闻...")
        raw_result = self.analyze_news(news_text)

        if not raw_result:
            print("分析失败，请检查API连接")
            return None

        print("解析分析结果...")
        print(type(raw_result))
        print(raw_result)

        with open("analysis_report.md", "w", encoding="utf-8") as f:
            f.write(raw_result)
        # parsed_data = self.parse_analysis_result(raw_result)

        print("生成分析报告...")
        # report = self.generate_report(parsed_data, news_text)

        # return report


# 使用示例
if __name__ == "__main__":
    # 替换为您的DeepSeek API密钥
    API_KEY = "***********************************"

    # 示例财经新闻
    financial_news = """
    商务部新闻发言人就美取消相关对华经贸限制措施情况答记者问。
    有记者问：近期，商务部新闻发言人表示，中美就伦敦会谈框架细节达成共识，
    “中方将依法审批符合条件的管制物项出口许可申请。美方将相应取消对华采取的一系列限制性措施。
    ”近日有报道称，相关企业已接到美国商务部通知，恢复EDA软件、乙烷、飞机发动机等产品对华出口。请问商务部对此有何评论？
     答：中美伦敦经贸会谈后，双方于近期确认了落实两国元首6月5日通话重要共识和巩固日内瓦经贸会谈成果的具体细节。
     目前，双方团队正在加紧落实伦敦框架有关成果。中方正依法依规审批符合条件的管制物项出口许可申请。美方也采取相应行动，取消对华采取的一系列限制性措施，有关情况已向中方作了通报。 伦敦框架来之不易，对话合作才是正道，讹诈胁迫没有出路。希望美方深刻认识中美经贸关系互利共赢的本质，
    继续同中方相向而行，进一步纠正错误做法，以实际行动维护和落实好两国元首通话重要共识，共同推动中美经贸关系行稳致远。
    """

    # 创建分析器实例
    analyst = DeepSeekFinancialAnalyst(API_KEY)

    # 执行分析并生成报告
    # report = analyst.analyze_and_report(financial_news)
    #
    # if report:
    #     print("\n" + "=" * 60)
    #     print("分析报告生成成功:")
    #     print("=" * 60)
    #     print(report)
    #     print("=" * 60)
    #
    #     # 保存报告到文件
    #     with open("financial_analysis_report.txt", "w", encoding="utf-8") as f:
    #         f.write(report)
    #     print("报告已保存至 financial_analysis_report.txt")
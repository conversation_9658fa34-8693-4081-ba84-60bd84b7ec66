import json
import time
from DrissionPage import ChromiumOptions, ChromiumPage
from DrissionPage import Chromium
from DrissionPage import WebPage,SessionPage

co = ChromiumOptions().headless(True)

# page = ChromiumPage(co)
# 启动或接管浏览器，并获取标签页对象
tab = ChromiumPage(co)
# 跳转到登录页面

tab.get('https://xueqiu.com')
# tab = tab.ele('.newLogin_active_2CK').click.for_new_tab()
# tab = tab.ele('tag:a',index=10).click()
# tab.ele('text:账号密码登录').click()
# 检查是否找到 <a> 标签
# if a_tags:
#     # 遍历所有 <a> 标签并打印其文本内容和 href 属性
#
#     print(f"文本内容: {a_tags.text}")
#     print(f"href 属性: {a_tags.attr('href')}")
# else:
#     print("未找到任何 <a> 标签")
# username = tab.eles('tag:input')
# for user in username:
#
#     print(user.text)
# tab.ele('@name=username').input('13817523120')
# tab.ele('@name=password').input('abcdE$1234')

# username_input = tab.ele('input[name="username"]')
#
# # 输入对文本框输入账号
# username_input.input('13817523120')
# password_input = tab.ele('input[name="password"]').input('abcdE$1234')

# 点击同意协议按钮
# tab.ele('text:阅读并同意').click()
# checkbox = tab.ele('text:阅读并同意')
# if checkbox:
#     checkbox.click()
# else:
#     print('同意协议复选框未找到')
# tab.ele('text:登录').click()
# time.sleep(10)
# 获取 cookies
print(f"页面标题: {tab.title}")
cookies = tab.cookies()

# 打印 cookies
print(cookies.as_json())
cookies_json = cookies.as_json()
cookies_data = json.loads(cookies_json)
cookies = {}
for item in cookies_data:
    cookies[item['name']] = item['value']
with open("xueqiucookies.txt", "w") as f:
    f.write(json.dumps(cookies))

tab.quit()
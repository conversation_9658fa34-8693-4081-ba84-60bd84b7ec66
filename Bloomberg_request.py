import json

import requests
from bs4 import BeautifulSoup

def get_headers_and_cookies():
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3542.0 Safari/537.36',
        "content-type": "application/json; charset=UTF-8",
        "Connection": "keep-alive"
    }
    with open("bloombergcookies.txt", "r") as f:
        cookies = f.read()
        cookies = json.loads(cookies)
    return headers, cookies

headers, cookies = get_headers_and_cookies()
# headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3542.0 Safari/537.36',
#         "content-type": "application/json; charset=UTF-8",
#         "Connection": "keep-alive"
#     }
url = "https://www.bloomberg.com/lineup-next/api/stories?limit=30&pageNumber=1&types=ARTICLE,FEATURE,INTERACTIVE,LETTER,EXPLAINERS"
r = requests.get(url, headers=headers,cookies=cookies)
print(r.text)

# soup = BeautifulSoup(r.text, 'html.parser')
# body_content = soup.find(class_='body-content')
# if body_content:
#     paragraphs = body_content.find_all('p')
#     for p in paragraphs:
#         print(f"段落内容: {p.text}")
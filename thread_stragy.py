import akshare as ak
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta

# 获取 A 股股票列表
def get_stock_list():
    try:
        stock_df = ak.stock_info_a_code_name()
        return stock_df[['code', 'name']].values.tolist()
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        return []

# 检测底部爆量信号
def detect_bottom_volume_signal(symbol, name):
    try:
        # 获取历史数据（至少需要40天数据以覆盖30日均线）
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=60)).strftime("%Y%m%d")
        df = ak.stock_zh_a_hist(symbol=symbol, period="daily", start_date=start_date, end_date=end_date, adjust="")

        # 数据预处理
        df.rename(columns={"日期": "date", "开盘": "open", "最高": "high", "最低": "low", "收盘": "close", "成交额": "volume"}, inplace=True)
        df["date"] = pd.to_datetime(df["date"])
        df.set_index("date", inplace=True)

        # 计算成交量均值
        df["volume_3d"] = df["volume"].rolling(window=3).mean()
        df["volume_30d"] = df["volume"].rolling(window=30).mean()
        df["volume_20d"] = df["volume"].rolling(window=20).mean()

        # 检查数据有效性（避免NaN）
        if df["volume_3d"].isna().iloc[-1] or df["volume_30d"].isna().iloc[-1] or df["volume_20d"].isna().iloc[-1]:
            return None

        # 条件1: 3日均值 > 30日均值的50%
        cond1 = df["volume_3d"].iloc[-1] > (df["volume_30d"].iloc[-1] * 0.5)
        # 条件2: 当日成交量 > 20日均值的70%
        cond2 = df["volume"].iloc[-1] > (df["volume_20d"].iloc[-1] * 0.7)

        # 综合信号
        if cond1 and cond2:
            return f"{symbol}({name})"
    except Exception as e:
        # print(f"处理 {symbol}({name}) 时出错: {e}")
        return None

# 批量筛选股票
def scan_stocks():
    stock_list = get_stock_list()
    results = []

    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(detect_bottom_volume_signal, symbol, name) for symbol, name in stock_list]
        for future in as_completed(futures):
            result = future.result()
            if result:
                results.append(result)

    return results

# 运行策略
if __name__ == "__main__":
    print("开始筛选底部爆量股票...")
    signals = scan_stocks()
    if signals:
        print("符合条件的股票：")
        for stock in signals:
            print(stock)
        with open("data.txt", "w", encoding="utf-8") as f:
            for item in signals:
                f.write(f"{item}\n")
    else:
        print("未发现符合条件的股票。")
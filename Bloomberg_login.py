import json
import time

from DrissionPage import Chromium
from DrissionPage import ChromiumOptions, ChromiumPage

# co = ChromiumOptions().headless(True)
# # 启动或接管浏览器，并获取标签页对象
# tab = Chromium().latest_tab
# # 跳转到登录页面
# tab.get('https://www.bloomberg.com/account/signin')
# time.sleep(20)
# # 定位到账号文本框，获取文本框元素
# ele = tab.ele('#email-form-input')
# # 输入对文本框输入账号
# ele.input('<EMAIL>')
# tab.ele('@tag()=button').click()
# # 定位到密码文本框并输入密码
# tab.ele('#password').input('<EMAIL>')
# # 点击登录按钮
# tab.ele('tag:button', index=1).click()
# time.sleep(10)
# # 获取 cookies
# cookies = tab.cookies()
#
# # 打印 cookies
# print(cookies.as_json())
# cookies_json = cookies.as_json()
cookies_json = [{"name": "NID", "value": "525=ab3O9ILFg747eUwqV6orqg2nX8xcpXAm08GSnZwce8re7oTjBihUoEdMe9202zZBPLYQne-DvfiS80lrxig2Hx4e2_g4PMVhekQNuKLYtaXtDZWBtefKJImtAZ5_PzqcLjNaGto2lMK5Ek5dlmZIhy2FvjtnRXp5PsKl7QmH6GrrIYuRA_qUeAFvd9oVoIMO9w", "domain": ".google.com"}, {"name": "IDE", "value": "AHWqTUlpZ5xx67ZbUoccUnTCpkKog-GpAGRKmR8giy_oLYs6ZZ3EFAyqvSwy-j6tPYI", "domain": ".doubleclick.net"}, {"name": "APC", "value": "AfxxVi6uTbYDcPFSqStElVwEsvxb9wb5OVeQ3mZaRhwB2lRUHOU0ag", "domain": ".doubleclick.net"}, {"name": "_pxde", "value": "fbaf0ebbe483842d5e95f672a28727c3aa0661d2229cae523220f93b1248242d:eyJ0aW1lc3RhbXAiOjE3NTA1MDkzNTU4MTIsImZfa2IiOjAsImlwY19pZCI6W119", "domain": ".bloomberg.com"}, {"name": "_reg-csrf-token", "value": "kOKKZoWR-xlIk4tzZ4XdrEBw6oNA01ZVk0Y8", "domain": ".bloomberg.com"}, {"name": "_user-data", "value": "%7B%22status%22%3A%22anonymous%22%7D", "domain": ".bloomberg.com"}, {"name": "_gcl_au", "value": "1.1.2096328619.1750486083.*********.1750509351.1750509351", "domain": ".bloomberg.com"}, {"name": "ar_debug", "value": "1", "domain": ".doubleclick.net"}, {"name": "_px2", "value": "eyJ1IjoiM2U1ZGUxNTAtNGU5Yy0xMWYwLWJiNTUtNzM4YmMzZWMzNjg4IiwidiI6IjBhNzQxNzk0LTRlNjYtMTFmMC1iZTJmLTQ1ODE2NWJhMTU5ZSIsInQiOjE3NTA1MDk2NDQxMzMsImgiOiJkYTRiNGE3OWExY2ZhODkyNGZkNTNiMmYxMjc4NGQyNDcyNTI0MDc4NTg4OTJmNDEyNTY2Mjg0MTIxN2QzZDE0In0=", "domain": ".bloomberg.com"}, {"name": "__gpi", "value": "UID=00001135884f5412:T=**********:RT=**********:S=ALNI_MbAZrd8pA7QT3oSkeaJ9P0M22bb3Q", "domain": ".bloomberg.com"}, {"name": "_parsely_visitor", "value": "{%22id%22:%22pid=757c6fe2-e2a4-4bb0-9372-7281f7cedbbd%22%2C%22session_count%22:2%2C%22last_session_ts%22:*************}", "domain": ".bloomberg.com"}, {"name": "_parsely_session", "value": "{%22sid%22:2%2C%22surl%22:%22https://www.bloomberg.com/account/signin%22%2C%22sref%22:%22https://www.bloomberg.com/account/signin%22%2C%22sts%22:*************%2C%22slts%22:*************}", "domain": ".bloomberg.com"}, {"name": "agent_id", "value": "********-92af-7535-ae86-0ba416fe3ca2", "domain": ".bloomberg.com"}, {"name": "_rdt_uuid", "value": "*************.f862b8ce-8078-47a6-b3de-989e8462eabd", "domain": ".bloomberg.com"}, {"name": "_pxhd", "value": "KrGjuD6h0f-BbRdo4NrDiv8WXSwi5cMYCQEYDrmUySceCmuhk85qnXSbZUvrTKg/WAaJIgr33RQVuWOAGRE6cQ==:UUoM9gjHDDuOs8D/AoD8E-xQgn6QU4e5PwkCTz/pT/66plj6l98NUJkk187DPSYNOz1M63lrVKE/wN94IdqNJZcng3d9FzSzkh1w3O2Vy5k=", "domain": "www.bloomberg.com"}, {"name": "__stripe_sid", "value": "15aa0bc0-25a9-4e1b-b1ec-86554e5e1bc896575a", "domain": ".www.bloomberg.com"}, {"name": "_session_id_backup", "value": "ad0d36a8-a175-420a-846e-40d13703d30c", "domain": ".bloomberg.com"}, {"name": "_scid_r", "value": "tDqG0qTryvk4MtCFDxI6uanPQlFCY5UlygpH-A", "domain": ".bloomberg.com"}, {"name": "_reg-csrf", "value": "s%3AesSagI4naHKs0uU6y5I3rWNA.Ny9mQeiUOS5DWjjoINRxbsaBzVT8SGvOevWI18KdKUU", "domain": ".bloomberg.com"}, {"name": "ttcsid", "value": "1750489252594::sP-EqZWRoXyjsG3VKLat.1.1750493045492", "domain": ".bloomberg.com"}, {"name": "__spdt", "value": "5d4c12ac0b4246a8b57567d0551f4c74", "domain": "www.bloomberg.com"}, {"name": "__gads", "value": "ID=e7372080ca55082d:T=**********:RT=**********:S=ALNI_ManMJy-HZYE_ouuW1QNgajyzCkXrA", "domain": ".bloomberg.com"}, {"name": "pxcts", "value": "38f79b48-4e9c-11f0-8498-e11ca0c2f93f", "domain": ".bloomberg.com"}, {"name": "_pxvid", "value": "0a741794-4e66-11f0-be2f-458165ba159e", "domain": ".bloomberg.com"}, {"name": "session_key", "value": "66e6aec660292eed2958edf9e662d3f354cf8e99", "domain": ".bloomberg.com"}, {"name": "ttcsid_CSN3O6BC77UF5CI6702G", "value": "1750489252594::68x48haUDQgBAi3oSm_u.1.1750493045783", "domain": ".bloomberg.com"}, {"name": "_sctr", "value": "1%7C1750435200000", "domain": ".bloomberg.com"}, {"name": "_uetsid", "value": "17ed10004e6611f0a938178387f9a659", "domain": ".bloomberg.com"}, {"name": "_scid", "value": "q7qG0qTryvk4MtCFDxI6uanPQlFCY5Ul", "domain": ".bloomberg.com"}, {"name": "_GRECAPTCHA", "value": "09ANMylNBP-sYv2S25lsKitT1NEenIME67-TktynHpSMR0cxVAVfi2ov2WayDCadxLEWm4UvXpKjyKAnh-rTtk93s", "domain": "www.google.com"}, {"name": "gatehouse_id", "value": "********-e5ab-7650-9b7f-402372bd4f56", "domain": ".bloomberg.com"}, {"name": "session_id", "value": "ad0d36a8-a175-420a-846e-40d13703d30c", "domain": ".bloomberg.com"}, {"name": "_tt_enable_cookie", "value": "1", "domain": ".bloomberg.com"}, {"name": "AF_SYNC", "value": "1750486620756", "domain": ".bloomberg.com"}, {"name": "_uetvid", "value": "17ed89304e6611f0b1f6ef2b53c7ed95", "domain": ".bloomberg.com"}, {"name": "panoramaIdType", "value": "panoDevice", "domain": ".bloomberg.com"}, {"name": "panoramaId_expiry", "value": "1751097847569", "domain": ".bloomberg.com"}, {"name": "__eoi", "value": "ID=04837c5c40b08f19:T=**********:RT=**********:S=AA-Afjb8cZETHHeqQXx6klLwMVnK", "domain": ".bloomberg.com"}, {"name": "_cc_id", "value": "11ac463a043fc14eb341b5ab7e94520e", "domain": ".bloomberg.com"}, {"name": "_pxhd", "value": "KrGjuD6h0f-BbRdo4NrDiv8WXSwi5cMYCQEYDrmUySceCmuhk85qnXSbZUvrTKg/WAaJIgr33RQVuWOAGRE6cQ==:UUoM9gjHDDuOs8D/AoD8E-xQgn6QU4e5PwkCTz/pT/66plj6l98NUJkk187DPSYNOz1M63lrVKE/wN94IdqNJZcng3d9FzSzkh1w3O2Vy5k=", "domain": "www.bloomberg.com"}, {"name": "consentUUID", "value": "7e0b79fa-6f2e-4589-938d-a0fe8cb7c996", "domain": ".bloomberg.com"}, {"name": "_scor_uid", "value": "b2c7ee17dfbe411c85205b4e5d075f2f", "domain": ".bloomberg.com"}, {"name": "afUserId", "value": "cd6313b3-7595-48a9-a80f-6c039e6a11ae-p", "domain": ".bloomberg.com"}, {"name": "usnatUUID", "value": "4ecef34b-f483-4903-9485-83c6a55bcf07", "domain": ".bloomberg.com"}, {"name": "_fbp", "value": "fb.1.1750486639445.842387138199925268", "domain": ".bloomberg.com"}, {"name": "AMP_4c214fdb8b", "value": "JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI3NzJlY2E5OC1mZThlLTRkYzUtYTg2OS01ZTNjMzRjNGJhODYlMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjI0NkU1NThDMThCOUI0NTUwQThENjZDNDA5RENEQzJDRCUyMiUyQyUyMnNlc3Npb25JZCUyMiUzQTE3NTA0ODY1ODM0OTAlMkMlMjJvcHRPdXQlMjIlM0FmYWxzZSUyQyUyMmxhc3RFdmVudFRpbWUlMjIlM0ExNzUwNDg5MzE5NjgxJTJDJTIybGFzdEV2ZW50SWQlMjIlM0E1MSUyQyUyMnBhZ2VDb3VudGVyJTIyJTNBMTclN0Q=", "domain": ".bloomberg.com"}, {"name": "_ttp", "value": "01JY8MMGQB8ACE3XNP0N2FQNVY_.tt.1", "domain": ".bloomberg.com"}, {"name": "geo_info", "value": "%7B%22countryCode%22%3A%22TW%22%2C%22country%22%3A%22TW%22%2C%22field_n%22%3A%22hf%22%2C%22trackingRegion%22%3A%22Asia%22%2C%22cacheExpiredTime%22%3A1751090882999%2C%22region%22%3A%22Asia%22%2C%22fieldN%22%3A%22hf%22%7D%7C1751090882999", "domain": ".bloomberg.com"}, {"name": "panoramaId", "value": "5bdeb906d4730c5a8bd0b5dfaaf3185ca02c761bfc93b643e0d3aba7e0d73195", "domain": ".bloomberg.com"}, {"name": "bbgconsentstring", "value": "req1fun1pad1", "domain": ".bloomberg.com"}, {"name": "_ga_GQ1PBLXZCT", "value": "GS2.1.s1750509325$o2$g1$t1750509353$j32$l0$h0", "domain": ".bloomberg.com"}, {"name": "optimizelyEndUserId", "value": "oeu1750486081288r0.39725259637151145", "domain": ".bloomberg.com"}, {"name": "__stripe_mid", "value": "ac2e70f0-b111-4500-bf13-f91cf45c89aa1a8f01", "domain": ".www.bloomberg.com"}, {"name": "_ga", "value": "GA1.1.406294398.1750486063", "domain": ".bloomberg.com"}, {"name": "AMP_MKTG_4c214fdb8b", "value": "JTdCJTdE", "domain": ".bloomberg.com"}, {"name": "receive-cookie-deprecation", "value": "1", "domain": ".doubleclick.net"}, {"name": "bdfpc", "value": "004.1662487486.1750486081561", "domain": ".www.bloomberg.com"}, {"name": "__sppvid", "value": "d34574db-e040-44fe-befe-3fc383c93efe", "domain": ".bloomberg.com"}]




# 转换为 key-value 形式的字典
result_dict = {item["name"]: item["value"] for item in cookies_json}

# 打印结果
for key, value in result_dict.items():
    print(f"{key}: {value}")

cookies = {}
# for item in result_dict:
#     cookies[item['name']] = item['value']
with open("bloombergcookies.txt", "w") as f:
    f.write(json.dumps(result_dict))
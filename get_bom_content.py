import re
from openai import OpenAI
import requests
from selenium.webdriver import Chrome
import time
import json
from selenium.webdriver import ChromeOptions
from selenium.webdriver.common.by import By
from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.orm import declarative_base
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
Base = declarative_base()

class Bloomberg(Base):
    __tablename__ = 'admin_bloomberg_news'
    id = Column(Integer, primary_key=True)
    title = Column(String(255))
    summary = Column(Text)
    url = Column(Text)
    published_at = Column(DateTime)
    content_html = Column(Text)
    image_url = Column(Text)
    bj_date = Column(DateTime)
    id_hash = Column(Text)
    content = Column(Text)
    article_content = Column(Text)
    article_trans_content = Column(Text)

DATABASE_URI = 'mysql+pymysql://root:root@127.0.0.1:3307/owl_admin'

engine = create_engine(DATABASE_URI)
Session = sessionmaker(bind=engine)
session = Session()

news = session.query(Bloomberg).filter(Bloomberg.id > 6937).all()
keyword = []
for new in news:
    keyword.append(new.url)

#print(keyword)
headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36',
    "content-type": "application/json; charset=UTF-8",
    "Connection": "keep-alive"
    }

# r = requests.get('https://archive.ph/kUi9C')
# print(r.text)
# exit(1)
from bs4 import BeautifulSoup
# option = webdriver.FirefoxOptions()
# option.add_argument('-headless')
# driver = webdriver.Firefox(executable_path='/Users/<USER>/Dropbox/Pythoncodes/Bleier/geckodriver')
option = ChromeOptions()
# 此步骤很重要，设置为开发者模式，防止被各大网站识别出来使用了Selenium
option.add_experimental_option('excludeSwitches', ['enable-automation'])
option.add_argument("--disable-blink-features")
#option.add_argument("--headless")
option.add_argument("--disable-blink-features=AutomationControlled")
# option.add_argument("--headless")
# option.add_argument("--disable-gpu")
# option.add_argument("--disable-javascript")
browser = Chrome(options=option)


#keyword = 'https://www.bloomberg.com/news/articles/2025-06-07/dhs-blames-california-democrats-as-ice-protests-enter-second-day'

client = OpenAI(
    base_url="http://localhost:11434/v1",  # Ollama API 地址
    api_key="ollamaxyza"  # Ollama 默认无需真实 API Key，填任意值即可
)


# 发送请求


def remove_think_tags(text):
    """
    移除文本中的所有<think>标签及其内容

    参数:
        text: 包含<think>标签的原始文本

    返回:
        清理后的文本
    """
    # 使用正则表达式匹配并移除<think>标签及其内容
    # 处理跨多行的标签内容
    pattern = r'<think>.*?</think>'
    cleaned_text = re.sub(pattern, '', text, flags=re.DOTALL)

    # 处理没有正确关闭的标签
    pattern_unclosed = r'<think>.*?$'
    cleaned_text = re.sub(pattern_unclosed, '', cleaned_text, flags=re.DOTALL)

    # 处理没有正确打开的标签
    pattern_unopened = r'^.*?</think>'
    cleaned_text = re.sub(pattern_unopened, '', cleaned_text, flags=re.DOTALL)

    return cleaned_text.strip()

system_prompt = (
    "你是一位专业的翻译专家，擅长将英文内容准确翻译成地道的中文。"
    "请遵循以下规则："
    "1. 保持原文的专业术语和核心含义不变"
    "2. 输出符合中文表达习惯的自然语言"
    "3. 对科技、商务等专业内容保持术语一致性"
    "4. 保留原文的段落结构和标点格式"
)

def translate_text(text):
    response = client.chat.completions.create(
        model="qwen3:8b",  # 指定模型

        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请翻译以下英文内容：\n\n{text}" }
        ],
        temperature=0.7,  # 控制生成多样性
        max_tokens=10240    # 最大生成 token 数
    )

    translation = response.choices[0].message.content
    translation = remove_think_tags(translation)
    return translation
def clean_text(text):
    """
    清洗文本内容：
    1. 移除 "save" 和 "Translate" 文本（不区分大小写）
    2. 移除 "Copy Link" 及之后的所有内容
    3. 清理多余的空格和换行符
    """
    # 第一步：移除 "sava" 和 "Translate"（不区分大小写）
    text = re.sub(r'\bsave\b|\bTranslate\b|\bListen\b', '', text, flags=re.IGNORECASE)

    # 第二步：移除 "Copy Link" 及之后的所有内容（不区分大小写）
    copy_link_pattern = re.compile(r'copy\s+link', re.IGNORECASE)
    match = copy_link_pattern.search(text)
    if match:
        text = text[:match.start()]

    # 第三步：清理多余的空白字符
    # 移除连续的空格和换行符
    # text = re.sub(r'\s+', ' ', text)
    # 移除首尾空白
    #text = text.strip()

    return text
def login(keyword):
    browser.get(
        "https://archive.is/")
    # 为了不透露个人信息，需要读者自己粘贴登陆界面的 url
    time.sleep(5)
    browser.find_element(By.XPATH,'//*[@id="q"]').send_keys(keyword)
    browser.find_element(By.XPATH, '//*[@id="search"]/div[3]/input').click()


# 登陆
for url in keyword:
    login(url)
    time.sleep(10)

# # 切换到跳出的小框

# browser.switch_to_frame("sp_message_iframe_490357")
# # 点击接受收集 Cookies
# browser.find_element_by_xpath("//button[@title='YES, I AGREE']").click()
    element = browser.find_element(By.XPATH, '//*[@id="row0"]/div[2]/a[1]')

    browser.get(element.get_attribute('href'))
# title = browser.find_element(By.XPATH, '//*[@id="__next"]/div/div[2]/main/div/article/div[2]/div/h1')
# print(title)
# page_text = browser.find_element(By.TAG_NAME, 'body').text
# print(page_text)

# divs = browser.find_elements(By.XPATH, '//*[@id="__next"]/div/div[2]/main/div/article/div[5]/div/div[2]/div[3]/div[2]')
#
#
# # 遍历并打印 div 元素的内容
# for div in divs:
#     print(div.text)

    title = browser.find_element(By.CSS_SELECTOR, "h1").text.strip()

# 获取更新时间（使用 XPath 定位）
    timestamp = browser.find_element(
        By.XPATH, "//time[contains(@datetime, '2025')]"
    ).get_attribute("datetime")



# 获取首张图片 URL
    image_element = browser.find_element(
        By.CSS_SELECTOR, "article img:first-of-type"
    )
    image_url = image_element.get_attribute("src")

# 获取正文内容
# main_content_sections = browser.find_element(
#     By.ID, '_next'
# )

    content_sections = browser.find_elements(By.CSS_SELECTOR, "article > div")

# 遍历并打印每个 div 的内容
# for div in content_sections:
#     print(div.text)

    content = "\n\n".join([p.text.strip() for p in content_sections if p.text.strip()])

    content = clean_text(content)

    print(f"\n正文内容:\n{content}")

    article_trans_content = translate_text(content)

    print(article_trans_content)
    article = session.query(Bloomberg).filter_by(url = url).first()
    print(article.title)
    article.article_content = content
    article.article_trans_content = article_trans_content

    # 提交更改
    session.commit()

    # 关闭会话
    session.close()
# time.sleep(5)
#
# # 将 Cookies 写入文件
# orcookies = browser.get_cookies()
# print(orcookies)
# cookies = {}
# for item in orcookies:
#     cookies[item['name']] = item['value']
# with open("wsjcookies.txt", "w") as f:
#     f.write(json.dumps(cookies))
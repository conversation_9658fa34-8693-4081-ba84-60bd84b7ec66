import requests

headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36',
    "Connection": "keep-alive"
}
cookie_str = "tmr_lvid=94c4bb4e421a82ef144738d112c666cd; tmr_lvidTS=1748942540134; domain_sid=L1Ab-Rcd-pjlP9ca0KgB0%3A1750059237975; _ga=GA1.2.661111166.1750059369; qki=4761292441076228631; tmr_detect=0%7C1750059379166"
proxies={
        "http": "http://127.0.0.1:7890",
        "https": "http://127.0.0.1:7890"
    }
# 将字符串按分号分割成多个键值对
cookie_pairs = cookie_str.split("; ")

# 创建一个空字典
cookie_dict = {}

# 遍历每个键值对
for pair in cookie_pairs:
    # 按等号分割键和值
    key, value = pair.split("=")
    # 将键值对存入字典
    cookie_dict[key.strip()] = value.strip()

url = 'https://archive.is/JmmUY'
res = requests.get(url, headers=headers, cookies=cookie_dict, proxies=proxies)
print(res.text)
# -*- coding: utf-8 -*-
"""
Created on 2025-06-30 16:05:17
---------
@summary:
---------
@author: Administrator
"""

import feapder


class FirstSpider(feapder.AirSpider):
    def start_requests(self):
        yield feapder.Request("https://www.gelonghui.com/api/live-channels/all/lives/v4?category=all&limit=15")

    def parse(self, request, response):
       print(response.json)


if __name__ == "__main__":
    FirstSpider().start()
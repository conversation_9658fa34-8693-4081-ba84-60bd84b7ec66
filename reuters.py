# import requests
#
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36',
# }
#
# url = 'https://www.reuters.com/pf/api/v3/content/fetch/articles-by-trends-v1?query=%7B%22size%22%3A4%2C%22website%22%3A%22reuters%22%7D&d=291&mxId=00000000&_website=reuters'
# cookie_dict = {}
# cookie_string = '_ga_WBSR7WLTGD=GS2.1.s1749998559$o3$g1$t1750000022$j41$l0$h0; _ga_WBSR7WLTGD=GS2.1.s1749998559$o3$g1$t1749999706$j41$l0$h0; usprivacy=1---; _gcl_au=1.1.1930137469.1747319144; _lc2_fpi=f511229f0ef8--01jva5cq5hwv2vxfvy0tfv7gtq; _cb=miyDrBNYYPTDZtzv; permutive-id=20c00b0e-32bf-4b66-bf22-25665feceb46; _fbp=fb.1.1747319166095.827990143971739789; ajs_anonymous_id=cec422a1-75c8-4cdb-a0c7-142cb2766122; _lr_env_src_ats=false; __binsUID=5e9f000b-74ff-4950-8400-30370b406804; _cc_id=ea2d63e5a2774a3864bc3b600822e3aa; OneTrustWPCCPAGoogleOptOut=false; _v__chartbeat3=By1c14t9SehDMSYNt; _lc2_fpi_meta=%7B%22w%22%3A1747402168547%7D; ccuid=7de09576-a88f-4896-a8f0-01bc4d8d46a2; gamera_user_id=ca3f26b5-4139-49ac-9e0e-5bd787f586f8; _au_1d=AU1D-0100-001747402171-VGRPLXA3-AWFT; _pubcid=91991c22-eb5d-46c2-b8c7-ade9b3166696; _pubcid_cst=VyxHLMwsHQ%3D%3D; pbjs-unifiedid=%7B%22TDID%22%3A%224af573e9-ca0e-4683-bc16-9f5c73588e91%22%2C%22TDID_LOOKUP%22%3A%22TRUE%22%2C%22TDID_CREATED_AT%22%3A%222025-04-16T14%3A31%3A37%22%7D; pbjs-unifiedid_cst=VyxHLMwsHQ%3D%3D; __idcontext=eyJjb29raWVJRCI6IjJ4Qk9JWExaN3NTUmh6dXdKeTd5YlNiemtQcCIsImRldmljZUlEIjoiMnhBc3ZPdXg0ck96NlptU1g0OWRNcGVBcGZSIiwiaXYiOiIiLCJ2IjoiIn0%3D; cleared-onetrust-cookies=Thu, 17 Feb 2022 19:17:07 GMT; pbjs_fabrickId_cst=VyxHLMwsHQ%3D%3D; cnx_userId=2-e6a39d728a324041bc037070e162a252; pbjs_fabrickId=%7B%22fabrickId%22%3A%22E1%3AOaS3M47Yg66tcx0CrQ8YoupoNAwsCt2F247yhfWnVBOGA5WoWodkbl_MH2jTOstkTn8ufeEP7C-TMEnDnd6CD8hgODdkFOHbdiTM77LsLOU%22%7D; _gid=GA1.2.1474574150.1749911437; krg_uid=%7B%22v%22%3A%7B%22clientId%22%3A%22d3966df2-5924-4153-9df2-d7b596b04a8a%22%2C%22userId%22%3A%22bd0667a1-405b-1c97-7049-9ae51c1732f5%22%2C%22optOut%22%3Afalse%7D%7D; uuid=E8928F60-6119-45AB-A10B-AEC24BBFB226; krg_crb=%7B%22v%22%3A%22eyJjbGllbnRJZCI6ImQzOTY2ZGYyLTU5MjQtNDE1My05ZGYyLWQ3YjU5NmIwNGE4YSIsInRkSUQiOiI0YWY1NzNlOS1jYTBlLTQ2ODMtYmMxNi05ZjVjNzM1ODhlOTEiLCJsZXhJZCI6ImJkMDY2N2ExLTQwNWItMWM5Ny03MDQ5LTlhZTUxYzE3MzJmNSIsImt0Y0lkIjoiODliYjcyMTgtODIxOC0wNjkwLTUzMzctNzc3ZDliN2IxNTIzIiwiZXhwaXJlVGltZSI6MTc0OTk5ODI0ODg1MSwibGFzdFN5bmNlZEF0IjoxNzQ5OTExNTM3MDk0LCJwYWdlVmlld0lkIjoiIiwicGFnZVZpZXdUaW1lc3RhbXAiOjE3NDk5MTE4NDg4MzgsInBhZ2VWaWV3VXJsIjoiaHR0cHM6Ly93d3cucmV1dGVycy5jb20vd29ybGQvY2hpbmEvIiwidXNwIjoiMS0tLSJ9%22%7D; _li_ss=CqcBCgYI-QEQgRsKBgj3ARCBGwoFCAoQgRsKBgjdARCBGwoGCIEBEIEbCgUIDBCLGwoGCPUBEIEbCgkI_____wcQhRsKBQgLEIEbCgYI4wEQghsKBgikARCBGwoGCLMBEIEbCgYIiQEQgRsKBgilARCBGwoGCIACEIQbCgYI4QEQgRsKBgiiARCBGwoGCP8BEIEbCgYI0gEQgRsKBQh-EIEbCgYIiAEQgRs; panoramaId_expiry=1750597564580; panoramaId=d1136d5e71753625fdc5ea17f39316d53938d2511d89a4a264c64e1662eb7035; panoramaIdType=panoIndiv; _parsely_visitor={%22id%22:%22pid=70979a20-3593-4aba-a82f-b6609d74da25%22%2C%22session_count%22:13%2C%22last_session_ts%22:1749998568297}; _ga_WBSR7WLTGD=GS2.1.s1749998559$o3$g0$t1749998568$j51$l0$h0; _chartbeat2=.1747319157639.1749999940821.0000000010100011.DWBhU0gIiay0mXVfBUtwTBr17OS.5; _awl=2.1749999941.5-b126c6a1203335e2ca8f9206a424a2d2-6763652d617369612d6561737431-0; _ga=GA1.1.2102192981.1747319177; _li_dcdm_c=.reuters.com; _lc2_fpi_js=f511229f0ef8--01jva5cq5hwv2vxfvy0tfv7gtq; cto_bundle=6D3qhF9PZWFsSXhRJTJGM0REbzhYNWNBanRZVHowMHNaZjVtb3FseGN0JTJGc1E1MzFsTjZTMFBXQ0JYWFdQY0dFcERoSFVMNmU5a0liSXRiV2VIempGb05xbkNCQjBmRWhnTDJUZXdOdW4lMkI1Uk1RVk1ZZTFHYXNrJTJCdW9KRjNDMEVrZWlEbmcxeWRCcGlZTFpjNmklMkJ2czlGWVRBUWE3OE85NUhPWFFOZ3ZhOGtCa0NJaVpvRlNkZE5SUzFOJTJCMGtyJTJCSHJ5MjNVVVRpZ1VONUpteGFGUkNjSDJrckl4MVElM0QlM0Q; _ga_FVWZ0RM4DH=GS2.1.s1750078961$o7$g0$t1750078961$j60$l0$h0; OptanonConsent=isGpcEnabled=0&datestamp=Mon+Jun+16+2025+21%3A02%3A57+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202505.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=79c9edbd-**************-150b823aaf36&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=1%3A1%2C2%3A1%2C3%3A1%2C4%3A1&AwaitingReconsent=false; ABTasty=uid=wsewpzbmzdjpd64v; ABTastySession=mrasn=&lp=https%253A%252F%252Fwww.reuters.com%252Fen%252Fpoodle-joins-dog-lifeguard-team-spanish-beach-2025-06-16%252F; __binsSID=09775d27-8bea-4add-9e4a-cca6902a1642; __gads=ID=eae74daedecf0601:T=1747319203:RT=1750078982:S=ALNI_MY4JwmHny6869mhtdHXBAfPHlMQLA; __gpi=UID=000010c2fa0ba34c:T=1747319203:RT=1750078982:S=ALNI_Ma3hxfLgP3Mr5cHgXnEAGhju6i62Q; __eoi=ID=48e5d1381598e0a5:T=1747319203:RT=1750078982:S=AA-AfjbZSJpXfgqzv7Ze-8FJoify; reuters-geo={"country":"-", "region":"-"}; datadome=JrzezOpXEcpN2n5_VbQ~iwrsPkHRTz6bYXC5WXCLzQXkBz2LaU8F1_ugtPRkiNskXrH1qNAyFdavCvZ9AbgboVd3LLcTahQlcND3u1Uj9nUojhv76VIevmsHFebAohJc; RT="z=1&dm=www.reuters.com&si=869d1e4f-8a38-427c-8354-ce673add1fe8&ss=mbz3nfgf&sl=2&tt=6th&rl=1&obo=1&ld=pd7r&r=z6uch9o&ul=pd7r"'
# for pair in cookie_string.split(';'):
#     pair = pair.strip()  # 去除多余的空格
#     if pair:  # 确保不是空字符串
#         key, value = pair.split('=', 1)  # 按等号分割，最多分割一次
#         cookie_dict[key] = value
# response = requests.get(url, headers=headers, cookies=cookie_dict)
# print(response.text)

from selenium.webdriver import Chrome
import time
import json
from selenium.webdriver import ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# Initialize the WebDriver
option = ChromeOptions()
# 此步骤很重要，设置为开发者模式，防止被各大网站识别出来使用了Selenium
option.add_experimental_option('excludeSwitches', ['enable-automation'])
option.add_argument("--disable-blink-features")
#option.add_argument("--headless")
option.add_argument("--disable-blink-features=AutomationControlled")
# option.add_argument("--headless")
# option.add_argument("--disable-gpu")
# option.add_argument("--disable-javascript")
browser = Chrome(options=option)

# URL of the Reuters article
# url = 'https://www.reuters.com/pf/api/v3/content/fetch/articles-by-trends-v1?query={"size":20,"website":"reuters"}&d=291&mxId=00000000&_website=reuters'
# Open the URL in the browser
# url = "https://www.reuters.com/world/china/"
# browser.get(url)
# wait = WebDriverWait(browser, 20)
# title_element = browser.find_element(By.CSS_SELECTOR, "h3[data-testid='Heading']")
# title = title_element.text
# print(title)
#
# elements = wait.until(
#     EC.presence_of_all_elements_located(
#         (By.CSS_SELECTOR, "li div[data-testid='MediaStoryCard'] h3[data-testid='Heading'] a[data-testid='Link']")
#     )
# )
#
# # 遍历所有匹配的元素
# for element in elements:
#     # 获取 href 属性
#     href = element.get_attribute("href")
#     # 获取文本内容
#     text = element.text
#     print(f"Href: {href}, Text: {text}")
# paragraph_elements = browser.find_elements(By.CSS_SELECTOR, "[data-testid^="paragraph-"]")
# json_text = browser.find_element("tag name", "pre").text  # 大多数API返回在<pre>标签中
#
# # 解析JSON
# json_data = json.loads(json_text)
# print(json_data)

url = 'https://www.reuters.com/business/aerospace-defense/taiwan-seals-ukraine-combat-tested-drone-software-deal-help-deter-china-2025-06-17/'
browser.get(url)

# Extract the title from the <h1> tag
title_element = browser.find_element(By.CSS_SELECTOR, "h1")
title = title_element.text

# Select all text elements
paragraph_elements = browser.find_elements(By.CSS_SELECTOR, '[data-testid^="paragraph-"]')

# Aggregate their text
content = " ".join(p.text for p in paragraph_elements)

# Prepare the data to be exported as JSON
article = {
    "title": title,
    "content": content
}

# Export data to a JSON file
with open("article.json", "w", encoding="utf-8") as json_file:
    json.dump(article, json_file, ensure_ascii=False, indent=4)